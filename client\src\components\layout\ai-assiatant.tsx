import React, { useState, useTransition } from 'react'
import { FaMicrophone, FaMicrophoneSlash } from 'react-icons/fa'
import { RxCross2 } from 'react-icons/rx'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '../ui/dialog'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../ui/tooltip'
import { Button } from '../ui/button'
import { LiveAudioVisualizer } from 'react-audio-visualize';
import { axiosInstance } from '@/lib/queryClient'
import { toast } from '@/hooks/use-toast'
// Import the webm-to-wav-converter library
import { getWaveBlob } from 'webm-to-wav-converter'

const AIAssistantDialog = ({ setOpen, open, isRecording, setIsRecording }: any) => {
    const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder>();
    const [audioChunks, setAudioChunks] = useState<Blob[]>([]);
    const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
    const [isPending, startTransition] = useTransition();

    return (
        <Dialog open={open} onOpenChange={(open) => {
            if (!open) {
                return;
            }
            setOpen(open);
        }} >
            <TooltipProvider>
                <Tooltip>
                    <TooltipTrigger asChild>
                        <DialogTrigger asChild>
                            <Button
                                size="lg"
                                className="fixed top-[75px] right-5 h-14 rounded-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold shadow-lg hover:shadow-xl transition-all group z-50"
                            >
                                <p className="text-2xl">🪄</p> Ask AI
                            </Button>
                        </DialogTrigger>
                    </TooltipTrigger>

                    <TooltipContent side="left" >
                        <p>AI Assistant</p>
                    </TooltipContent>

                </Tooltip>
            </TooltipProvider>

            <DialogContent className="min-w-96 bg-white min-h-[500px] !rounded-[.7rem] [&>button]:hidden">
                <DialogHeader>
                    <DialogTitle>AI Assistant</DialogTitle>
                </DialogHeader>
                <div className="grid gap-4 py-2">
                    <p>How can I help you with this exercise?</p>
                    <div className="flex flex-col gap-4">
                        <div className="relative w-full h-[300px] bg-gray-100 rounded-xl">
                            {/* Video preview will be shown here */}
                            <video
                                className="w-full h-full object-cover rounded-xl"
                                id="video-preview"
                            />
                        </div>

                        <div className="flex items-center justify-center gap-6">
                            <button
                                className="p-3 rounded-full text-white transition-colors border-2"
                                aria-label="Toggle microphone"
                                onClick={async () => {
                                    if (isRecording) {
                                        // Stop recording
                                        if (mediaRecorder) {
                                            mediaRecorder.stop();
                                            
                                            // Set up the stop event handler
                                            mediaRecorder.onstop = async () => {
                                                try {
                                                    console.log('Audio chunks:', audioChunks);
                                                    
                                                    // Convert WebM chunks to WAV using the library
                                                    const wavBlob = await getWaveBlob(audioChunks, true); // false for 16-bit, true for 32-bit
                                                    setAudioBlob(wavBlob);
                                                    
                                                    console.log('Converted WAV audio blob:', wavBlob);

                                                    // startTransition(() => {
                                                    //     const sendAudio = async () => {
                                                    //         try {
                                                    //             // Create FormData to send the WAV file
                                                    //             const formData = new FormData();
                                                    //             formData.append('audio', wavBlob, 'recording.wav');

                                                    //             await axiosInstance.post('/ai/transcribe', formData, {
                                                    //                 headers: {
                                                    //                     'Content-Type': 'multipart/form-data',
                                                    //                 },
                                                    //             });
                                                                
                                                    //             toast({
                                                    //                 title: 'Success',
                                                    //                 description: 'Audio sent successfully',
                                                    //             });
                                                    //         } catch (error) {
                                                    //             console.error('Error sending audio:', error);
                                                    //             toast({
                                                    //                 title: 'Error',
                                                    //                 description: 'Something went wrong',
                                                    //                 variant: 'destructive',
                                                    //             });
                                                    //         }
                                                    //     };
                                                    //     sendAudio();
                                                    // });
                                                } 
                                                catch (error) {
                                                    console.error('Error converting audio:', error);
                                                    toast({
                                                        title: 'Error',
                                                        description: 'Failed to convert audio',
                                                        variant: 'destructive',
                                                    });
                                                }
                                            };
                                        }
                                        
                                        setMediaRecorder(undefined);
                                        setIsRecording(false);
                                        setAudioChunks([]);
                                    } else {
                                        // Start recording
                                        try {
                                            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                                            const recorder = new MediaRecorder(stream);
                                            
                                            // Clear previous chunks
                                            setAudioChunks([]);
                                            
                                            // Set up data available handler
                                            recorder.ondataavailable = (event) => {
                                                if (event.data.size > 0) {
                                                    setAudioChunks(chunks => [...chunks, event.data as Blob]);
                                                }
                                            };
                                            
                                            setMediaRecorder(recorder);
                                            recorder.start(100); // Collect data every 100ms
                                            setIsRecording(true);
                                        } catch (error) {
                                            console.error('Error starting recording:', error);
                                            toast({
                                                title: 'Error',
                                                description: 'Failed to start recording',
                                                variant: 'destructive',
                                            });
                                        }
                                    }
                                }}
                            >
                                {isRecording ? (
                                    <FaMicrophone
                                        size={28}
                                        color="red"
                                    />
                                ) : (
                                    <FaMicrophoneSlash
                                        size={28}
                                        color="gray"
                                    />
                                )}
                            </button>

                            <RxCross2
                                size={55}
                                onClick={() => {
                                    setOpen(false);
                                    setIsRecording(false);
                                    if (mediaRecorder) {
                                        mediaRecorder.stop();
                                        setMediaRecorder(undefined);
                                    }
                                    setAudioChunks([]);
                                }}
                                color="gray"
                                className="bg-gray-100 text-black border-2 p-[14px] font-bold rounded-full cursor-pointer"
                            />
                        </div>
                    </div>
                </div>
                {mediaRecorder && (
                    <div className='w-full overflow-hidden'>
                        <LiveAudioVisualizer
                            mediaRecorder={mediaRecorder}
                            width={600}
                            height={60}
                            barWidth={2}
                            gap={1}
                            fftSize={512}
                        />
                    </div>
                )}
            </DialogContent>
        </Dialog>
    )
}

export default AIAssistantDialog